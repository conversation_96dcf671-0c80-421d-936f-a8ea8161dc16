import { getDMMF } from "@prisma/internals";
import path from "path";
import fs from "fs";

// Cache for model field information
let modelFieldsCache: Record<string, {
  fields: string[];
  relations: Record<string, string[]>;
}> = {};

// Initialize the model fields cache
async function initializeModelFieldsCache() {
  if (Object.keys(modelFieldsCache).length > 0) {
    return modelFieldsCache;
  }

  try {
    // Read all schema files and merge them
    const schemaDir = path.join(process.cwd(), "prisma", "schema");
    const schemaFiles = fs.readdirSync(schemaDir).filter(file => file.endsWith('.prisma'));

    let mergedSchema = '';
    for (const file of schemaFiles) {
      const filePath = path.join(schemaDir, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      mergedSchema += content + '\n';
    }

    const dmmf = await getDMMF({ datamodel: mergedSchema });

    // Process all models
    dmmf.datamodel.models.forEach((model) => {
      const modelName = model.name.toLowerCase();
      const fields: string[] = [];
      const relations: Record<string, string[]> = {};

      model.fields.forEach((field) => {
        if (field.kind === "scalar" || field.kind === "enum") {
          // Add scalar and enum fields
          fields.push(field.name);
        } else if (field.kind === "object") {
          // Handle relation fields
          const relatedModel = dmmf.datamodel.models.find(m => m.name === field.type);
          if (relatedModel) {
            relations[field.name] = relatedModel.fields
              .filter(f => f.kind === "scalar" || f.kind === "enum")
              .map(f => f.name);
          }
        }
      });

      modelFieldsCache[modelName] = { fields, relations };
    });

    return modelFieldsCache;
  } catch (error) {
    console.error("Failed to initialize model fields cache:", error);
    throw error;
  }
}

// Get all sortable fields for a model
export async function getModelSortableFields(modelName: string): Promise<string[]> {
  await initializeModelFieldsCache();

  const normalizedModelName = modelName.toLowerCase();
  const modelInfo = modelFieldsCache[normalizedModelName];

  if (!modelInfo) {
    throw new Error(`Model '${modelName}' not found in schema`);
  }

  const sortableFields = [...modelInfo.fields];

  // Add relation fields in format "relation.field"
  Object.entries(modelInfo.relations).forEach(([relationName, relationFields]) => {
    relationFields.forEach(field => {
      sortableFields.push(`${relationName}.${field}`);
    });
  });

  return sortableFields;
}

// Professional sort helper that automatically works with any Prisma model
export async function getModelOrderBy(
  modelName: string,
  sortByArr: string | string[],
  orderArr: string | string[],
  defaultField: string = "id",
  defaultOrder: "asc" | "desc" = "asc"
) {
  // Get all sortable fields for the model
  const allowedFields = await getModelSortableFields(modelName);

  // Ensure arrays are properly handled
  if (!Array.isArray(sortByArr)) {
    sortByArr = sortByArr ? [String(sortByArr)] : [];
  }
  if (!Array.isArray(orderArr)) {
    orderArr = orderArr ? [String(orderArr)] : [];
  }

  // Filter out empty or invalid fields
  const validSortFields = sortByArr.filter(field => field && String(field).trim());

  if (validSortFields.length === 0) {
    // Return default sorting
    return [{ [defaultField]: defaultOrder }];
  }

  return validSortFields.map((rawField, idx) => {
    const inputField = String(rawField).trim().toLowerCase();
    const orderValue = orderArr[idx] ? String(orderArr[idx]).trim().toLowerCase() : "asc";
    const sortOrder = orderValue === "desc" ? "desc" : "asc";

    // Find matching allowed field in a case-insensitive way
    const matchedField = allowedFields.find((f) => f.toLowerCase() === inputField);

    if (!matchedField) {
      // If field not found, use default field
      return { [defaultField]: sortOrder };
    }

    if (matchedField.includes(".")) {
      const [relation, nestedField] = matchedField.split(".");
      return {
        [relation]: {
          [nestedField]: sortOrder,
        },
      };
    }

    return { [matchedField]: sortOrder };
  });
}

// Legacy function for backward compatibility
export function getOrderBy(
  sortByArr: string[],
  orderArr: string[],
  allowedFields: string[]
) {
  if (!allowedFields.length) {
    throw new Error("No allowed fields defined for sorting.");
  }

  // Ensure arrays are properly handled
  if (!Array.isArray(sortByArr)) {
    sortByArr = sortByArr ? [String(sortByArr)] : [];
  }
  if (!Array.isArray(orderArr)) {
    orderArr = orderArr ? [String(orderArr)] : [];
  }

  // Filter out empty or invalid fields
  const validSortFields = sortByArr.filter(field => field && String(field).trim());

  if (validSortFields.length === 0) {
    // Return default sorting by first allowed field if no valid sort fields
    return [{ [allowedFields[0]]: "asc" }];
  }

  return validSortFields.map((rawField, idx) => {
    const inputField = String(rawField).trim().toLowerCase();
    const orderValue = orderArr[idx] ? String(orderArr[idx]).trim().toLowerCase() : "asc";
    const sortOrder = orderValue === "desc" ? "desc" : "asc";

    // Find matching allowed field in a case-insensitive way
    const matchedField =
      allowedFields.find((f) => f.toLowerCase() === inputField) || allowedFields[0];

    if (matchedField.includes(".")) {
      const [relation, nestedField] = matchedField.split(".");
      return {
        [relation]: {
          [nestedField]: sortOrder,
        },
      };
    }

    return { [matchedField]: sortOrder };
  });
}
