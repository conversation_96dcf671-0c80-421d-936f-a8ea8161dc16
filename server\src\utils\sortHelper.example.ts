/**
 * Professional Sort Helper Usage Examples
 * 
 * This file demonstrates how to use the new getModelOrderBy function
 * that automatically detects all Prisma model fields for sorting.
 */

import { getModelOrderBy, getModelSortableFields } from "./sortHelper";

// Example 1: Simple controller with automatic field detection
export const viewWorkReportsWithAutoSort = async (req: any, res: any) => {
  try {
    const { sortBy = "id", order = "desc" } = req.query;
    
    // Automatically detects ALL WorkReport fields including relations
    const orderBy = await getModelOrderBy("WorkReport", sortBy, order, "id", "desc");
    
    const data = await prisma.workReport.findMany({
      orderBy,
      include: {
        user: true,
        client: true,
        carrier: true,
        work_type: true,
        category: true,
      },
    });
    
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Example 2: User controller with automatic field detection
export const viewUsersWithAutoSort = async (req: any, res: any) => {
  try {
    const { sortBy = "id", order = "desc" } = req.query;
    
    // Automatically detects ALL User fields including relations like role.name, branch.branch_name
    const orderBy = await getModelOrderBy("User", sortBy, order, "created_at", "desc");
    
    const data = await prisma.user.findMany({
      orderBy,
      include: {
        role: true,
        branch: true,
        userClients: {
          include: {
            client: true,
          },
        },
      },
    });
    
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Example 3: Corporation controller with automatic field detection
export const viewCorporationsWithAutoSort = async (req: any, res: any) => {
  try {
    const { sortBy = "corporation_id", order = "desc" } = req.query;
    
    // Automatically detects ALL Corporation fields
    const orderBy = await getModelOrderBy("Corporation", sortBy, order, "corporation_id", "desc");
    
    const data = await prisma.corporation.findMany({
      orderBy,
    });
    
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Example 4: TrackSheets controller with automatic field detection
export const viewTrackSheetsWithAutoSort = async (req: any, res: any) => {
  try {
    const { sortBy = "id", order = "desc" } = req.query;
    
    // Automatically detects ALL TrackSheets fields including client relations
    const orderBy = await getModelOrderBy("TrackSheets", sortBy, order, "id", "desc");
    
    const data = await prisma.trackSheets.findMany({
      orderBy,
      include: {
        client: true,
      },
    });
    
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Example 5: Get all sortable fields for a model (useful for debugging)
export const getModelFields = async (req: any, res: any) => {
  try {
    const { modelName } = req.params;
    
    const sortableFields = await getModelSortableFields(modelName);
    
    return res.status(200).json({
      model: modelName,
      sortableFields,
      totalFields: sortableFields.length
    });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Frontend Usage Examples:
 * 
 * // Single column sort
 * GET /api/workreports?sortBy=date&order=desc
 * GET /api/workreports?sortBy=client.client_name&order=asc
 * GET /api/workreports?sortBy=user.username&order=desc
 * 
 * // Multiple column sort
 * GET /api/workreports?sortBy=date,client.client_name&order=desc,asc
 * GET /api/users?sortBy=role.name,created_at&order=asc,desc
 * 
 * // With pagination
 * GET /api/workreports?page=1&pageSize=50&sortBy=time_spent&order=desc
 * 
 * // Complex sorting with filters
 * GET /api/workreports?client_name=ABC&sortBy=date,actual_number&order=desc,asc
 */

/**
 * Benefits of the New Sort Helper:
 * 
 * 1. ✅ Automatic Field Detection: No need to manually define allowedFields arrays
 * 2. ✅ Relation Support: Automatically handles relation fields like user.username, client.client_name
 * 3. ✅ Type Safety: Works with TypeScript and Prisma types
 * 4. ✅ Performance: Caches model information for better performance
 * 5. ✅ Maintainability: When you add new fields to Prisma models, they're automatically sortable
 * 6. ✅ Consistency: Same API across all controllers
 * 7. ✅ Error Handling: Graceful fallback to default sorting if invalid fields are provided
 * 
 * Migration from Old System:
 * 
 * OLD WAY:
 * ```typescript
 * const allowedFields = ["id", "date", "user.username", "client.client_name"];
 * const orderBy = getOrderBy(mappedSortByArr, orderArr, allowedFields);
 * ```
 * 
 * NEW WAY:
 * ```typescript
 * const orderBy = await getModelOrderBy("WorkReport", sortBy, order, "id", "desc");
 * ```
 */
